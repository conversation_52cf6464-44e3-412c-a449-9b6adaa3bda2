import React, { useState, useEffect } from "react";
import { FaBars, FaTimes, FaUser, FaCog, FaSignOutAlt } from "react-icons/fa";
import { motion, AnimatePresence } from "framer-motion";
import { Link, useNavigate } from "react-router-dom";
import useAuth from "../hooks/useAuth";

// Base navigation items for all users
const BASE_NAV_ITEMS = [
  { name: "About Us", path: "/about" },
  { name: "Services", path: "/Services" },
  { name: "How It Works", path: "/howItWorks" },
  { name: "Contact", path: "/contact" },
];

// Role-specific navigation items
const ROLE_NAV_ITEMS = {
  admin: [
    { name: "Admin Dashboard", path: "/admin-dashboard" },
    { name: "User Management", path: "/admin-dashboard/users" },
    { name: "Companies", path: "/admin-dashboard/companies" },
    { name: "<PERSON><PERSON><PERSON>", path: "/admin-dashboard/settings" },
  ],
  company: [
    { name: "Dashboard", path: "/dashboard" },
    { name: "Create Job", path: "/job-create" },
    { name: "Test Management", path: "/test-management" },
    { name: "Aptitude", path: "/aptitude" },
    { name: "Interview", path: "/interview" },
    { name: "Profile", path: "/profile" },
  ],
  student: [
    { name: "Dashboard", path: "/student/home" },
    { name: "Jobs", path: "/student/jobs" },
    { name: "Applications", path: "/student/applications" },
    { name: "Tests", path: "/student/tests" },
    { name: "Results", path: "/student/results" },
    { name: "Profile", path: "/student/profile" },
  ],
};

const navVariants = {
  hidden: { y: -70, opacity: 0 },
  visible: { y: 0, opacity: 1, transition: { duration: 0.6, ease: "easeOut" } },
};

const mobileItemVariants = {
  hidden: { x: -30, opacity: 0 },
  visible: (i) => ({
    x: 0,
    opacity: 1,
    transition: {
      delay: i * 0.07 + 0.1,
      type: "spring",
      stiffness: 300,
      damping: 24,
    },
  }),
};

const Navbar = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [showProfileMenu, setShowProfileMenu] = useState(false);
  const navigate = useNavigate();

  const {
    user,
    isAuthenticated,
    loading,
    logout
  } = useAuth();

  // Get navigation items based on user authentication and role
  const getNavigationItems = () => {
    if (!isAuthenticated) {
      return BASE_NAV_ITEMS;
    }

    const roleItems = ROLE_NAV_ITEMS[user?.role] || [];
    return [...BASE_NAV_ITEMS, ...roleItems];
  };

  const handleToggle = () => setMenuOpen((prev) => !prev);
  const handleNavClick = () => setMenuOpen(false);

  const handleLogin = () => {
    navigate('/login');
    setMenuOpen(false);
  };

  const handleLogout = async () => {
    await logout();
    setShowProfileMenu(false);
    setMenuOpen(false);
    navigate('/');
  };

  const handleProfileClick = () => {
    setShowProfileMenu(!showProfileMenu);
  };

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Close profile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showProfileMenu && !event.target.closest('.profile-menu-container')) {
        setShowProfileMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showProfileMenu]);

  // Optional: Redirect to dashboard after login
  /*
  useEffect(() => {
    if (isAuthenticated) {
      navigate("/dashboard");
    }
  }, [isAuthenticated, navigate]);
  */

  // Text color logic
  const textColor = scrolled ? "text-gray-800" : "text-white";

  // Desktop nav items
  const desktopNavItems = getNavigationItems();

  // Mobile nav items
  const mobileNavItems = desktopNavItems;

  return (
    <motion.nav
      variants={navVariants}
      initial="hidden"
      animate="visible"
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        scrolled
          ? "bg-white/95 backdrop-blur-lg shadow-md py-4"
          : "bg-transparent py-3"
      }`}
    >
      <div className="max-w-7xl mx-auto px-6 flex justify-between items-center">
        {/* Logo */}
        <Link
          to={isAuthenticated ? "/dashboard-redirect" : "/"}
          className={`text-2xl font-bold tracking-tight ${textColor}`}
        >
          Proyuj
        </Link>

        {/* Desktop Menu */}
        <ul className="hidden lg:flex space-x-8 items-center">
          {desktopNavItems.map((item) => (
            <li key={item.name}>
              <Link
                to={item.path}
                className="text-gray-800 hover:text-[#fcb045] font-medium transition-colors duration-300"
              >
                {item.name}
              </Link>
            </li>
          ))}
          <li>
            {!loading && !isAuthenticated && (
              <>
                <button
                  onClick={handleLogin}
                  className="ml-4 bg-[#fcb045] text-white px-6 py-2 rounded-full font-bold shadow hover:scale-105 transition-transform duration-300"
                >
                  <FaUser className="inline-block mr-2" />
                  Login
                </button>
                <Link
                  to="/register"
                  className="ml-4 bg-[#34c759] text-white px-6 py-2 rounded-full font-bold shadow hover:scale-105 transition-transform duration-300 inline-flex items-center"
                >
                  <FaUser className="inline-block mr-2" />
                  Sign Up
                </Link>
              </>
            )}
            {!loading && isAuthenticated && (
              <div className="relative flex items-center gap-2 profile-menu-container">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold">
                    {user?.name?.charAt(0)?.toUpperCase() || user?.email?.charAt(0)?.toUpperCase() || 'U'}
                  </div>
                  <span className="font-medium">{user?.name || user?.email}</span>
                  <span className="text-xs bg-gray-200 px-2 py-1 rounded-full capitalize">
                    {user?.role}
                  </span>
                </div>
                <button
                  onClick={handleProfileClick}
                  className="ml-2 p-1 rounded-full hover:bg-gray-100 transition-colors"
                >
                  <FaCog className="text-gray-600" />
                </button>

                {/* Profile Dropdown */}
                {showProfileMenu && (
                  <div className="absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border py-2 z-50">
                    <Link
                      to="/profile"
                      className="block px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors"
                      onClick={() => setShowProfileMenu(false)}
                    >
                      <FaUser className="inline mr-2" />
                      Profile
                    </Link>
                    <button
                      onClick={handleLogout}
                      className="w-full text-left px-4 py-2 text-red-600 hover:bg-red-50 transition-colors"
                    >
                      <FaSignOutAlt className="inline mr-2" />
                      Logout
                    </button>
                  </div>
                )}
              </div>
            )}
          </li>
        </ul>

        {/* Hamburger */}
        <button
          aria-label="Toggle menu"
          className={`lg:hidden text-2xl ${textColor}`}
          onClick={handleToggle}
        >
          {menuOpen ? <FaTimes /> : <FaBars />}
        </button>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {menuOpen && (
          <>
            <motion.div
              key="mobile-menu"
              initial={{ x: "100%", opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: "100%", opacity: 0 }}
              transition={{ type: "spring", stiffness: 400, damping: 35 }}
              className="lg:hidden fixed top-0 left-0 w-full h-full z-50 bg-white/95 backdrop-blur-lg shadow-md"
            >
              <div className="relative z-10 flex flex-col items-center justify-center h-full space-y-10 pt-16">
                <nav className="w-full flex flex-col items-center gap-3">
                  {mobileNavItems.map((item, index) => (
                    <motion.a
                      key={item.name}
                      href={item.path}
                      className="text-xl font-medium text-gray-800 hover:text-[#fcb045] transition-colors duration-300 px-6 py-2"
                      onClick={handleNavClick}
                      custom={index}
                      initial="hidden"
                      animate="visible"
                      exit="hidden"
                      variants={mobileItemVariants}
                    >
                      {item.name}
                    </motion.a>
                  ))}
                </nav>
                {!loading && !isAuthenticated && (
                  <>
                    <motion.button
                      initial={{ scale: 0.9, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      exit={{ scale: 0.8, opacity: 0 }}
                      transition={{ delay: 0.35 }}
                      className="bg-[#fcb045] text-white px-10 py-3 rounded-full font-bold text-lg shadow hover:scale-105 transition-transform duration-300"
                      onClick={() => {
                        handleLogin();
                        handleNavClick();
                      }}
                    >
                      <FaUser className="inline-block mr-2" />
                      Login
                    </motion.button>
                    <motion.div
                      initial={{ scale: 0.9, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      exit={{ scale: 0.8, opacity: 0 }}
                      transition={{ delay: 0.38 }}
                    >
                      <Link
                        to="/register"
                        className="bg-[#34c759] text-white px-10 py-3 rounded-full font-bold text-lg shadow hover:scale-105 transition-transform duration-300 inline-flex items-center"
                        onClick={handleNavClick}
                      >
                        <FaUser className="inline-block mr-2" />
                        Sign Up
                      </Link>
                    </motion.div>
                  </>
                )}
                {!loading && isAuthenticated && (
                  <div className="flex flex-col items-center gap-3">
                    <div className="w-16 h-16 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold text-2xl">
                      {user?.name?.charAt(0)?.toUpperCase() || user?.email?.charAt(0)?.toUpperCase() || 'U'}
                    </div>
                    <div className="text-center">
                      <span className="font-medium text-lg block">{user?.name || user?.email}</span>
                      <span className="text-sm bg-gray-200 px-2 py-1 rounded-full capitalize">
                        {user?.role}
                      </span>
                    </div>
                    <motion.button
                      initial={{ scale: 0.9, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      exit={{ scale: 0.8, opacity: 0 }}
                      transition={{ delay: 0.41 }}
                      className="bg-red-500 text-white px-10 py-3 rounded-full font-bold text-lg shadow hover:scale-105 transition-transform duration-300"
                      onClick={() => {
                        handleLogout();
                        handleNavClick();
                      }}
                    >
                      <FaSignOutAlt className="inline-block mr-2" />
                      Logout
                    </motion.button>
                  </div>
                )}
              </div>
            </motion.div>
            {/* Overlay */}
            <motion.div
              key="overlay"
              initial={{ opacity: 0 }}
              animate={{ opacity: 0.4 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-40 bg-black/50"
              onClick={handleToggle}
            />
          </>
        )}
      </AnimatePresence>
    </motion.nav>
  );
};

export default Navbar;
