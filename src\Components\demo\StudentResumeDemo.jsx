/**
 * Demo component to test student resume functionality
 * This component demonstrates the resume template with mock data matching the API structure
 */

import React, { useState } from 'react';

// Mock data matching the API response structure you provided
const mockResumeData = {
  success: true,
  data: {
    profile: {
      _id: "687b91d629bed4831477d6f1",
      Title: "<PERSON><PERSON><PERSON>",
      Email: "<EMAIL>",
      Template: "default",
      isPublished: false,
      PublishURL: null,
      PublicAccess: false,
      LastExported: null,
      PDFLink: null,
      Tags: [],
      Order: [],
      Profiles: [
        {
          Network: "GitHub",
          Username: "adarsh-kumar",
          ProfileLink: "https://github.com/adarsh-kumar",
          ProfileImage: "https://cdn.simpleicons.org/github",
          _id: "687b93da29bed4831477d752"
        },
        {
          Network: "LinkedIn",
          Username: "adarsh-kumar-dev",
          ProfileLink: "https://linkedin.com/in/adarsh-kumar-dev",
          ProfileImage: "https://cdn.simpleicons.org/linkedin",
          _id: "687b93da29bed4831477d753"
        }
      ],
      Experience: [
        {
          Company: "TechCorp Solutions",
          Position: "Full Stack Developer",
          StartDate: "2023-06-01T00:00:00.000Z",
          EndDate: "2025-01-15T00:00:00.000Z",
          Location: "Bengaluru, India",
          Website: "https://techcorp.com",
          Description: "Developed and maintained web applications using React, Node.js, and MongoDB. Led a team of 3 junior developers and implemented CI/CD pipelines.",
          _id: "687b942529bed4831477d757"
        },
        {
          Company: "StartupXYZ",
          Position: "Frontend Developer Intern",
          StartDate: "2022-12-01T00:00:00.000Z",
          EndDate: "2023-05-31T00:00:00.000Z",
          Location: "Remote",
          Website: "https://startupxyz.com",
          Description: "Built responsive user interfaces and improved application performance by 40%.",
          _id: "687b942529bed4831477d758"
        }
      ],
      Education: [
        {
          Institution: "National Institute of Science and Technology",
          Degree: "Bachelor of Technology in Computer Science",
          StartDate: "2020-08-01T00:00:00.000Z",
          EndDate: "2024-06-30T00:00:00.000Z",
          Location: "Berhampur, Odisha",
          _id: "687b94aa29bed4831477d767"
        }
      ],
      Skills: [
        {
          Skill: "JavaScript",
          Proficiency: "Expert",
          Keywords: ["React", "Node.js", "Express", "ES6+"],
          Description: "5+ years of experience in JavaScript development",
          _id: "687b94ba29bed4831477d772"
        },
        {
          Skill: "Python",
          Proficiency: "Intermediate",
          Keywords: ["Django", "Flask", "Data Analysis"],
          Description: "Used for backend development and data analysis",
          _id: "687b94c729bed4831477d77f"
        },
        {
          Skill: "Database Management",
          Proficiency: "Advanced",
          Keywords: ["MongoDB", "PostgreSQL", "Redis"],
          Description: "Experience with both SQL and NoSQL databases",
          _id: "687b94c729bed4831477d780"
        }
      ],
      Languages: [
        {
          Name: "English",
          Proficiency: "Fluent",
          Description: "Professional working proficiency",
          _id: "687b94d029bed4831477d78e"
        },
        {
          Name: "Hindi",
          Proficiency: "Native",
          Description: "Mother tongue",
          _id: "687b94d829bed4831477d79f"
        },
        {
          Name: "Odia",
          Proficiency: "Native",
          Description: "Regional language",
          _id: "687b94e229bed4831477d7b2"
        }
      ],
      Awards: [
        {
          Title: "Best Student Developer Award",
          Issuer: "NIST College",
          Date: "2024-05-15T00:00:00.000Z",
          Description: "Awarded for outstanding performance in software development projects",
          _id: "687b94f629bed4831477d7c7"
        },
        {
          Title: "Hackathon Winner",
          Issuer: "TechFest 2023",
          Date: "2023-11-20T00:00:00.000Z",
          Description: "First place in 48-hour coding competition",
          _id: "687b94f629bed4831477d7c8"
        }
      ],
      Certifications: [
        {
          Title: "AWS Certified Developer",
          Issuer: "Amazon Web Services",
          Date: "2024-03-10T00:00:00.000Z",
          Website: "https://aws.amazon.com/certification/",
          Description: "Associate level certification for AWS development",
          _id: "687b949829bed4831477d75e"
        },
        {
          Title: "React Professional Certificate",
          Issuer: "Meta",
          Date: "2023-08-15T00:00:00.000Z",
          Website: "https://www.coursera.org/professional-certificates/meta-react-native",
          Description: "Comprehensive React development certification",
          _id: "687b949829bed4831477d75f"
        }
      ],
      Interests: [
        { Interest: "Open Source Contribution" },
        { Interest: "Machine Learning" },
        { Interest: "Photography" },
        { Interest: "Travel" }
      ],
      Publications: [
        {
          Title: "Modern Web Development with React and Node.js",
          Publisher: "Tech Journal",
          Date: "2024-01-15T00:00:00.000Z",
          Website: "https://techjournal.com/articles/modern-web-dev",
          Description: "A comprehensive guide to full-stack development",
          _id: "687b956929bed4831477d7f7"
        }
      ],
      Volunteering: [
        {
          Organization: "Code for Community",
          Position: "Technical Coordinator",
          StartDate: "2023-01-01T00:00:00.000Z",
          EndDate: "2024-12-31T00:00:00.000Z",
          Location: "Bengaluru",
          Description: "Leading technical initiatives for social impact projects",
          _id: "687b958b29bed4831477d812"
        }
      ],
      References: [
        {
          Name: "Dr. Rajesh Kumar",
          Position: "Professor",
          Company: "NIST",
          Email: "<EMAIL>",
          Phone: "+91-9876543210",
          _id: "687b95bf29bed4831477d82f"
        },
        {
          Name: "Priya Sharma",
          Position: "Senior Developer",
          Company: "TechCorp Solutions",
          Email: "<EMAIL>",
          Phone: "+91-9876543211",
          _id: "687b95bf29bed4831477d830"
        }
      ],
      Projects: [
        {
          Title: "E-Commerce Platform",
          Description: "Full-stack e-commerce application with payment integration, user authentication, and admin dashboard. Built using MERN stack.",
          Link: "https://github.com/adarsh-kumar/ecommerce-platform",
          Technologies: ["React", "Node.js", "MongoDB", "Stripe API", "JWT"],
          StartDate: "2023-09-01T00:00:00.000Z",
          EndDate: "2024-01-15T00:00:00.000Z",
          _id: "687b953929bed4831477d7de"
        },
        {
          Title: "Task Management System",
          Description: "Collaborative task management application with real-time updates, file sharing, and team collaboration features.",
          Link: "https://github.com/adarsh-kumar/task-manager",
          Technologies: ["React", "Express", "Socket.io", "PostgreSQL"],
          StartDate: "2023-03-01T00:00:00.000Z",
          EndDate: "2023-08-30T00:00:00.000Z",
          _id: "687b953929bed4831477d7df"
        }
      ],
      createdAt: "2025-01-19T12:38:46.538Z",
      updatedAt: "2025-01-19T12:56:05.586Z",
      __v: 0,
      Headline: "Full Stack Developer | React & Node.js Specialist",
      Website: "https://adarsh-kumar.dev",
      Phone: "+91-9749038945",
      Location: "Bengaluru, Karnataka, India",
      summery: "Passionate full-stack developer with 3+ years of experience in building scalable web applications. Expertise in React, Node.js, and cloud technologies. Strong problem-solving skills and a collaborative team player.",
      ProfileImagePublicId: "profile_pictures/user_1752929756304",
      ProfilePic: "https://res.cloudinary.com/dhqwyqekj/image/upload/v1752929765/profile_pictures/user_1752929756304.jpg"
    },
    completionStatus: 95
  }
};

const StudentResumeDemo = () => {
  const [showMockData, setShowMockData] = useState(false);

  // Extract data from mock response
  const resumeData = mockResumeData.data.profile;
  
  const {
    Title,
    Email,
    Headline,
    Phone,
    Location,
    Website,
    ProfilePic,
    Profiles = [],
    Experience = [],
    Education = [],
    Skills = [],
    Languages = [],
    Certifications = [],
    Awards = [],
    Projects = [],
    Publications = [],
    Volunteering = [],
    References = [],
    Interests = [],
    summery = "",
  } = resumeData;

  const formatDate = (date) => {
    if (!date) return 'Present';
    return new Date(date).toLocaleDateString();
  };

  const Section = ({ title, content }) => (
    <div className="mb-8">
      <h2 className="text-2xl font-semibold border-b-2 border-blue-300 mb-3">
        {title}
      </h2>
      <div className="space-y-2 text-sm text-gray-800">{content}</div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-[#23414c] mb-2">Student Resume Demo</h1>
          <p className="text-gray-600 mb-4">
            This demonstrates how the resume template renders with actual API data structure
          </p>
          <button
            onClick={() => setShowMockData(!showMockData)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            {showMockData ? 'Hide' : 'Show'} Mock Data Structure
          </button>
        </div>

        {showMockData && (
          <div className="mb-8 bg-gray-800 text-green-400 p-4 rounded-lg overflow-auto max-h-96">
            <h3 className="text-lg font-semibold mb-2 text-white">API Response Structure:</h3>
            <pre className="text-xs">{JSON.stringify(mockResumeData, null, 2)}</pre>
          </div>
        )}

        {/* Resume Template */}
        <div className="max-w-6xl mx-auto p-8 bg-gradient-to-br from-white to-blue-50 rounded-3xl shadow-xl font-sans text-gray-800">
          {/* Header */}
          <div className="flex flex-col md:flex-row items-center gap-6 border-b pb-6 mb-8">
            {ProfilePic && (
              <img
                src={ProfilePic}
                alt="Profile"
                className="w-28 h-28 rounded-xl object-cover shadow-md"
              />
            )}
            <div className="text-center md:text-left">
              <h1 className="text-4xl font-bold text-blue-900">{Title}</h1>
              {Headline && <p className="text-lg text-blue-700">{Headline}</p>}
              <div className="mt-2 space-y-1 text-sm text-gray-600">
                {Email && (
                  <p>
                    ✉️{" "}
                    <a href={`mailto:${Email}`} className="underline text-blue-700">
                      {Email}
                    </a>
                  </p>
                )}
                {Phone && <p>📞 {Phone}</p>}
                {Location && <p>📍 {Location}</p>}
                {Website && (
                  <p>
                    🌐{" "}
                    <a
                      href={Website}
                      target="_blank"
                      rel="noreferrer"
                      className="underline text-blue-700"
                    >
                      {Website}
                    </a>
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Sections */}
          {summery && <Section title="Summary" content={<p>{summery}</p>} />}
          
          {Profiles.length > 0 && (
            <Section
              title="Social Profiles"
              content={Profiles.map((p) => (
                <p key={p._id}>
                  <strong>{p.Network}:</strong>{" "}
                  <a
                    href={p.ProfileLink}
                    target="_blank"
                    rel="noreferrer"
                    className="text-blue-600 underline"
                  >
                    {p.Username}
                  </a>
                </p>
              ))}
            />
          )}

          {/* Continue with other sections... */}
          {Experience.length > 0 && (
            <Section
              title="Experience"
              content={Experience.map((exp) => (
                <div key={exp._id} className="mb-4">
                  <h3 className="text-lg font-semibold">
                    {exp.Position} at {exp.Company}
                  </h3>
                  <p className="italic text-sm text-gray-500">{exp.Location}</p>
                  <p className="text-sm">
                    {formatDate(exp.StartDate)} - {formatDate(exp.EndDate)}
                  </p>
                  {exp.Description && (
                    <p className="text-sm mt-1">{exp.Description}</p>
                  )}
                  {exp.Website && (
                    <a
                      href={exp.Website}
                      target="_blank"
                      rel="noreferrer"
                      className="text-blue-600 text-sm underline"
                    >
                      {exp.Website}
                    </a>
                  )}
                </div>
              ))}
            />
          )}

          {/* Add more sections as needed */}
        </div>
      </div>
    </div>
  );
};

export default StudentResumeDemo;
