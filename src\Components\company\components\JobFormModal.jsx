import React, { useCallback, useState, useEffect } from 'react';
import { XMarkIcon, BriefcaseIcon, MapPinIcon, CurrencyDollarIcon, CalendarIcon, CheckCircleIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { motion, AnimatePresence } from 'framer-motion';

const JobFormModal = ({
    showForm,
    handleCloseForm,
    handleSubmit,
    handleChange,
    handleRequirementChange,
    handleTechStackChange,
    form,
    error,
    success,
    loading
}) => {
    const [currentStep, setCurrentStep] = useState(1);
    const [formErrors, setFormErrors] = useState({});
    const [isFormValid, setIsFormValid] = useState(false);

    const steps = [
        { id: 1, name: 'Basic Info', icon: BriefcaseIcon },
        { id: 2, name: 'Details', icon: MapPinIcon },
        { id: 3, name: 'Compensation', icon: CurrencyDollarIcon },
        { id: 4, name: 'Settings', icon: CalendarIcon }
    ];

    // Validate form fields
    const validateForm = useCallback(() => {
        const errors = {};

        // Basic validation
        if (!form.title?.trim()) errors.title = 'Job title is required';
        if (!form.description?.trim()) errors.description = 'Job description is required';
        if (!form.requirements?.length) errors.requirements = 'Requirements are required';
        if (!form.techStack?.length) errors.techStack = 'Tech stack is required';
        if (!form.category) errors.category = 'Category is required';
        if (!form.location?.trim()) errors.location = 'Location is required';
        if (!form.salary?.min || form.salary.min <= 0) errors.salaryMin = 'Minimum salary is required';
        if (!form.salary?.max || form.salary.max <= 0) errors.salaryMax = 'Maximum salary is required';
        if (form.salary?.min >= form.salary?.max) errors.salaryRange = 'Maximum salary must be greater than minimum';
        if (!form.salary?.currency?.trim()) errors.currency = 'Currency is required';
        if (!form.applicationDeadline) errors.applicationDeadline = 'Application deadline is required';
        if (!form.maxApplications || form.maxApplications <= 0) errors.maxApplications = 'Max applications is required';

        // Date validation
        if (form.applicationDeadline && new Date(form.applicationDeadline) <= new Date()) {
            errors.applicationDeadline = 'Application deadline must be in the future';
        }

        setFormErrors(errors);
        setIsFormValid(Object.keys(errors).length === 0);
        return Object.keys(errors).length === 0;
    }, [form]);

    useEffect(() => {
        validateForm();
    }, [validateForm]);

    const fieldVariants = {
        hidden: { opacity: 0, scale: 0.95, y: 20 },
        visible: (i) => ({
            opacity: 1,
            scale: 1,
            y: 0,
            transition: {
                delay: 0.1 + i * 0.08,
                duration: 0.4,
                type: 'spring',
                stiffness: 100,
                damping: 15
            }
        }),
        exit: { opacity: 0, scale: 0.95, y: -20, transition: { duration: 0.2 } }
    };

    const stepVariants = {
        hidden: { opacity: 0, x: 50 },
        visible: { opacity: 1, x: 0, transition: { duration: 0.3 } },
        exit: { opacity: 0, x: -50, transition: { duration: 0.3 } }
    };

    // Helper function to handle nested state updates
    const handleNestedChange = (e) => {
        const { name, value } = e.target;
        const [parent, child] = name.split('.');
        handleChange({ target: { name: parent, value: { ...form[parent], [child]: value } } });
    };

    const nextStep = () => {
        if (currentStep < steps.length) {
            setCurrentStep(currentStep + 1);
        }
    };

    const prevStep = () => {
        if (currentStep > 1) {
            setCurrentStep(currentStep - 1);
        }
    };

    const handleFormSubmit = (e) => {
        e.preventDefault();
        if (validateForm()) {
            handleSubmit(e);
        }
    };

    const renderFormField = (label, name, type = 'text', options = {}) => {
        const {
            placeholder,
            required = false,
            icon: Icon,
            isTextarea = false,
            isSelect = false,
            selectOptions = [],
            min,
            max,
            step
        } = options;

        const fieldError = formErrors[name.replace('.', '')] || formErrors[name];
        const fieldValue = name.includes('.') ?
            form[name.split('.')[0]]?.[name.split('.')[1]] :
            form[name];

        return (
            <div className="space-y-2">
                <label htmlFor={name} className="flex items-center gap-2 text-sm font-semibold text-gray-800">
                    {Icon && <Icon className="w-4 h-4 text-black" />}
                    {label}
                    {required && <span className="text-red-500">*</span>}
                </label>

                <div className="relative">
                    {isSelect ? (
                        <select
                            id={name}
                            name={name}
                            value={fieldValue || ''}
                            onChange={name.includes('.') ? handleNestedChange : handleChange}
                            className={`w-full border rounded-xl px-4 py-3.5 text-gray-800 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm ${fieldError ? 'border-red-300 bg-red-50' : 'border-gray-200 hover:border-gray-300'
                                }`}
                            required={required}
                        >
                            {selectOptions.map(option => (
                                <option key={option.value} value={option.value}>{option.label}</option>
                            ))}
                        </select>
                    ) : isTextarea ? (
                        <textarea
                            id={name}
                            name={name}
                            value={fieldValue || ''}
                            onChange={handleChange}
                            className={`w-full border rounded-xl px-4 py-3.5 text-gray-800 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical min-h-[120px] bg-white shadow-sm ${fieldError ? 'border-red-300 bg-red-50' : 'border-gray-200 hover:border-gray-300'
                                }`}
                            placeholder={placeholder}
                            required={required}
                        />
                    ) : (
                        <input
                            type={type}
                            id={name}
                            name={name}
                            value={fieldValue || ''}
                            onChange={name.includes('.') ? handleNestedChange : handleChange}
                            className={`w-full border rounded-xl px-4 py-3.5 text-gray-800 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm ${fieldError ? 'border-red-300 bg-red-50' : 'border-gray-200 hover:border-gray-300'
                                }`}
                            placeholder={placeholder}
                            required={required}
                            min={min}
                            max={max}
                            step={step}
                        />
                    )}

                    {fieldError && (
                        <motion.div
                            className="flex items-center gap-1 mt-1 text-red-600 text-xs"
                            initial={{ opacity: 0, y: -5 }}
                            animate={{ opacity: 1, y: 0 }}
                        >
                            <ExclamationTriangleIcon className="w-3 h-3" />
                            {fieldError}
                        </motion.div>
                    )}
                </div>
            </div>
        );
    };

    const renderStep = () => {
        switch (currentStep) {
            case 1:
                return (
                    <div className="space-y-6">
                        {renderFormField('Job Title', 'title', 'text', {
                            placeholder: 'e.g. Senior React Developer',
                            required: true,
                            icon: BriefcaseIcon
                        })}

                        {renderFormField('Job Description', 'description', 'text', {
                            placeholder: 'Describe the role and responsibilities in detail... no of test >10',
                            required: true,
                            isTextarea: true
                        })}

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {renderFormField('Category', 'category', 'text', {
                                required: true,
                                isSelect: true,
                                selectOptions: [
                                    { value: '', label: 'Select Category' },
                                    { value: 'Frontend', label: 'Frontend Development' },
                                    { value: 'Backend', label: 'Backend Development' },
                                    { value: 'Full Stack', label: 'Full Stack Development' },
                                    { value: 'Data Science', label: 'Data Science' },
                                    { value: 'DevOps', label: 'DevOps & Infrastructure' },
                                    { value: 'Mobile', label: 'Mobile Development' },
                                    { value: 'UI/UX', label: 'UI/UX Design' },
                                    { value: 'QA', label: 'Quality Assurance' },
                                    { value: 'Other', label: 'Other' }
                                ]
                            })}

                            {renderFormField('Job Type', 'jobType', 'text', {
                                required: true,
                                isSelect: true,
                                selectOptions: [
                                    { value: 'Full-time', label: 'Full-time' },
                                    { value: 'Part-time', label: 'Part-time' },
                                    { value: 'Contract', label: 'Contract' },
                                    { value: 'Internship', label: 'Internship' }
                                ]
                            })}
                        </div>
                    </div>
                );

            case 2:
                return (
                    <div className="space-y-6">
                        {renderFormField('Requirements', 'requirements', 'text', {
                            placeholder: 'e.g. 2+ years React experience, REST API knowledge (separate with commas)',
                            required: true
                        })}

                        {renderFormField('Tech Stack', 'techStack', 'text', {
                            placeholder: 'e.g. React, JavaScript, Tailwind CSS, Node.js (separate with commas)',
                            required: true
                        })}

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {renderFormField('Experience Level', 'experienceLevel', 'text', {
                                required: true,
                                isSelect: true,
                                selectOptions: [
                                    { value: 'Entry', label: 'Entry Level (0-2 years)' },
                                    { value: 'Mid', label: 'Mid Level (2-5 years)' },
                                    { value: 'Senior', label: 'Senior Level (5+ years)' },
                                    { value: 'Lead', label: 'Lead/Principal (8+ years)' }
                                ]
                            })}

                            {renderFormField('Work Mode', 'workMode', 'text', {
                                required: true,
                                isSelect: true,
                                selectOptions: [
                                    { value: 'Remote', label: 'Remote' },
                                    { value: 'On-site', label: 'On-site' },
                                    { value: 'Hybrid', label: 'Hybrid' }
                                ]
                            })}
                        </div>

                        {renderFormField('Location', 'location', 'text', {
                            placeholder: 'e.g. New York, NY or Remote',
                            required: true,
                            icon: MapPinIcon
                        })}
                    </div>
                );

            case 3:
                return (
                    <div className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            {renderFormField('Minimum Salary', 'salary.min', 'number', {
                                placeholder: 'e.g. 60000',
                                required: true,
                                min: 0,
                                icon: CurrencyDollarIcon
                            })}

                            {renderFormField('Maximum Salary', 'salary.max', 'number', {
                                placeholder: 'e.g. 100000',
                                required: true,
                                min: 0,
                                icon: CurrencyDollarIcon
                            })}

                            {renderFormField('Currency', 'salary.currency', 'text', {
                                placeholder: 'e.g. USD, INR, EUR',
                                required: true
                            })}
                        </div>

                        {formErrors.salaryRange && (
                            <motion.div
                                className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm"
                                initial={{ opacity: 0, y: -10 }}
                                animate={{ opacity: 1, y: 0 }}
                            >
                                <ExclamationTriangleIcon className="w-4 h-4" />
                                {formErrors.salaryRange}
                            </motion.div>
                        )}

                        <div className="p-6 bg-gray-50 rounded-xl border border-gray-200">
                            <h3 className="text-sm font-semibold text-gray-900 mb-2 flex items-center gap-2">
                                <CurrencyDollarIcon className="w-4 h-4" />
                                Salary Preview
                            </h3>
                            <p className="text-gray-800">
                                {form.salary?.min && form.salary?.max && form.salary?.currency ?
                                    `${form.salary.currency} ${parseInt(form.salary.min).toLocaleString()} - ${parseInt(form.salary.max).toLocaleString()}` :
                                    'Fill in salary details to see preview'
                                }
                            </p>
                        </div>
                    </div>
                );

            case 4:
                return (
                    <div className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {renderFormField('Application Deadline', 'applicationDeadline', 'datetime-local', {
                                required: true,
                                icon: CalendarIcon
                            })}

                            {renderFormField('Maximum Applications', 'maxApplications', 'number', {
                                placeholder: 'e.g. 200',
                                required: true,
                                min: 1
                            })}
                        </div>

                        <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-xl border border-gray-200">
                            <input
                                type="checkbox"
                                id="hasTest"
                                name="hasTest"
                                checked={form.hasTest || false}
                                onChange={(e) => handleChange({ target: { name: 'hasTest', value: e.target.checked } })}
                                className="w-5 h-5 text-blue-600 border-2 border-gray-300 rounded focus:ring-2 focus:ring-blue-500 transition-all"
                            />
                            <label htmlFor="hasTest" className="text-sm font-medium text-gray-800">
                                Include technical assessment/test
                            </label>
                        </div>

                        {/* Form Summary */}
                        <div className="p-6 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-200">
                            <h3 className="text-sm font-semibold text-green-900 mb-3 flex items-center gap-2">
                                <CheckCircleIcon className="w-4 h-4" />
                                Job Posting Summary
                            </h3>
                            <div className="text-sm text-green-800 space-y-1">
                                <p><span className="font-medium">Title:</span> {form.title || 'Not specified'}</p>
                                <p><span className="font-medium">Category:</span> {form.category || 'Not specified'}</p>
                                <p><span className="font-medium">Type:</span> {form.jobType || 'Not specified'}</p>
                                <p><span className="font-medium">Location:</span> {form.location || 'Not specified'}</p>
                                <p><span className="font-medium">Work Mode:</span> {form.workMode || 'Not specified'}</p>
                            </div>
                        </div>
                    </div>
                );

            default:
                return null;
        }
    };

    return (
        <AnimatePresence>
            {showForm && (
                <motion.div
                    className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60 p-4 backdrop-blur-sm"
                    onClick={(e) => e.target === e.currentTarget && handleCloseForm()}
                    role="dialog"
                    aria-modal="true"
                    aria-labelledby="modal-title"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                >
                    <motion.div
                        className="relative bg-white rounded-3xl shadow-2xl w-full max-w-4xl h-[95vh] flex flex-col overflow-hidden"
                        initial={{ scale: 0.9, opacity: 0, y: 20 }}
                        animate={{ scale: 1, opacity: 1, y: 0 }}
                        exit={{ scale: 0.9, opacity: 0, y: 20 }}
                        transition={{ duration: 0.3, type: 'spring', stiffness: 300, damping: 30 }}
                    >
                        {/* Header */}
                        <div className="bg-gradient-to-r from-[rgb(35,65,75)] to-gray-900 text-white p-6 relative overflow-hidden">
                            <div className="absolute inset-0 bg-black bg-opacity-10"></div>
                            <div className="relative z-10">
                                <button
                                    className="absolute top-0 right-0 text-white/80 hover:text-white transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-white/50 rounded-full p-2"
                                    onClick={handleCloseForm}
                                    aria-label="Close form"
                                >
                                    <XMarkIcon className="w-6 h-6" />
                                </button>

                                <div className="flex items-center gap-3 mb-4">
                                    <BriefcaseIcon className="w-8 h-8" />
                                    <div>
                                        <h2 id="modal-title" className="text-2xl font-bold">Create Job Posting</h2>
                                        <p className="text-white/90 text-sm">Step {currentStep} of {steps.length}</p>
                                    </div>
                                </div>

                                {/* Progress Steps */}
                                <div className="flex items-center gap-2">
                                    {steps.map((step, index) => {
                                        const StepIcon = step.icon;
                                        const isActive = currentStep === step.id;
                                        const isCompleted = currentStep > step.id;

                                        return (
                                            <div key={step.id} className="flex items-center">
                                                <div className={`flex items-center gap-2 px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-200 ${isActive
                                                        ? 'bg-white text-blue-600'
                                                        : isCompleted
                                                            ? 'bg-white/20 text-white'
                                                            : 'bg-white/10 text-white/60'
                                                    }`}>
                                                    <StepIcon className="w-3 h-3" />
                                                    {step.name}
                                                </div>
                                                {index < steps.length - 1 && (
                                                    <div className={`w-8 h-0.5 mx-1 transition-colors duration-200 ${isCompleted ? 'bg-white/40' : 'bg-white/20'
                                                        }`} />
                                                )}
                                            </div>
                                        );
                                    })}
                                </div>
                            </div>
                        </div>

                        {/* Content */}
                        <div className="flex flex-col h-[calc(95vh-140px)]">
                            <div className="flex-1 overflow-y-auto p-6">
                                {error && (
                                    <motion.div
                                        className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl flex items-center gap-2"
                                        initial={{ opacity: 0, y: -10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        role="alert"
                                        aria-live="polite"
                                    >
                                        <ExclamationTriangleIcon className="w-5 h-5" />
                                        {error}
                                    </motion.div>
                                )}

                                {success && (
                                    <motion.div
                                        className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-xl flex items-center gap-2"
                                        initial={{ opacity: 0, y: -10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        role="alert"
                                        aria-live="polite"
                                    >
                                        <CheckCircleIcon className="w-5 h-5" />
                                        Job created successfully! 🎉
                                    </motion.div>
                                )}

                                <div onSubmit={handleFormSubmit}>
                                    <AnimatePresence mode="wait">
                                        <motion.div
                                            key={`step-${currentStep}`}
                                            variants={stepVariants}
                                            initial="hidden"
                                            animate="visible"
                                            exit="exit"
                                        >
                                            {renderStep()}
                                        </motion.div>
                                    </AnimatePresence>
                                </div>
                            </div>

                            {/* Footer */}
                            <div className="flex-shrink-0 border-t border-gray-200 p-6 bg-gray-50">
                                <div className="flex justify-between items-center">
                                    <div className="flex gap-3">
                                        {currentStep > 1 && (
                                            <button
                                                type="button"
                                                onClick={prevStep}
                                                className="px-6 py-3 text-gray-600 border border-gray-300 rounded-xl font-medium hover:bg-gray-100 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-400"
                                            >
                                                Previous
                                            </button>
                                        )}
                                    </div>

                                    <div className="flex gap-3">
                                        {currentStep < steps.length ? (
                                            <button
                                                type="button"
                                                onClick={nextStep}
                                                className="px-8 py-3 bg-gradient-to-r from-[rgb(35,65,75)] to-gray-900 text-white rounded-xl font-medium hover:from-[rgb(45,75,85)] hover:to-gray-800 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 shadow-lg hover:shadow-xl transform hover:scale-105"
                                            >
                                                Next Step
                                            </button>
                                        ) : (
                                            <button
                                                type="submit"
                                                disabled={loading || !isFormValid}
                                                onClick={handleFormSubmit}
                                                className={`px-8 py-3 rounded-xl font-medium transition-all duration-200 focus:outline-none focus:ring-2 shadow-lg hover:shadow-xl transform hover:scale-105 ${loading || !isFormValid
                                                        ? 'bg-gray-400 text-white cursor-not-allowed'
                                                        : 'bg-gradient-to-r from-[rgb(35,65,75)] to-gray-900 text-white hover:from-[rgb(45,75,85)] hover:to-gray-800 focus:ring-gray-500'
                                                    }`}
                                            >
                                                {loading ? (
                                                    <div className="flex items-center gap-2">
                                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                                        Creating...
                                                    </div>
                                                ) : (
                                                    'Create Job Posting'
                                                )}
                                            </button>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    );
};

export default JobFormModal;